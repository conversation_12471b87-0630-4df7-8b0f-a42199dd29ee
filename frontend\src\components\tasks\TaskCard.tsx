import React from 'react';
import { Clock, MoreVertical, CheckCircle2, Circle, Pause, X } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useUpdateTaskStatus } from '@/hooks/useTasks';
import { formatDate, cn, getContextStyles, getPriorityStyles } from '@/utils/helpers';
import { CONTEXTS, PRIORITIES, STATUSES } from '@/utils/constants';
import type { Task, TaskStatus } from '@/types';

interface TaskCardProps {
  task: Task;
  compact?: boolean;
}

export function TaskCard({ task, compact = false }: TaskCardProps) {
  const updateTaskStatus = useUpdateTaskStatus();

  const handleStatusChange = (newStatus: TaskStatus) => {
    updateTaskStatus.mutate({ taskId: task.id, status: newStatus });
  };

  const getStatusIcon = () => {
    switch (task.status) {
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-secondary-600" />;
      case 'in_progress':
        return <Pause className="h-5 w-5 text-primary-600" />;
      case 'cancelled':
        return <X className="h-5 w-5 text-red-600" />;
      default:
        return <Circle className="h-5 w-5 text-neutral-400" />;
    }
  };

  const contextConfig = CONTEXTS[task.context];
  const priorityConfig = PRIORITIES[task.priority];
  const statusConfig = STATUSES[task.status];

  return (
    <Card 
      hover 
      className={cn(
        'transition-all duration-200',
        task.completed && 'opacity-75',
        compact ? 'p-3' : 'p-4'
      )}
    >
      <div className="flex items-start space-x-3">
        {/* Status Icon */}
        <button
          onClick={() => {
            if (task.status === 'completed') {
              handleStatusChange('pending');
            } else {
              handleStatusChange('completed');
            }
          }}
          className="mt-0.5 transition-colors hover:scale-110"
          disabled={updateTaskStatus.isLoading}
        >
          {getStatusIcon()}
        </button>

        {/* Task Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className={cn(
                'font-medium text-neutral-900',
                task.completed && 'line-through text-neutral-500',
                compact ? 'text-sm' : 'text-base'
              )}>
                {task.title}
              </h3>
              
              {task.description && !compact && (
                <p className="mt-1 text-sm text-neutral-600 line-clamp-2">
                  {task.description}
                </p>
              )}
            </div>

            {/* More Actions */}
            <button className="ml-2 rounded-lg p-1 text-neutral-400 hover:bg-neutral-100 hover:text-neutral-600">
              <MoreVertical className="h-4 w-4" />
            </button>
          </div>

          {/* Task Metadata */}
          <div className="mt-3 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {/* Context Badge */}
              <span className={cn(
                'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                getContextStyles(task.context)
              )}>
                <span className="mr-1">{contextConfig.icon}</span>
                {contextConfig.label}
              </span>

              {/* Priority Badge */}
              <span className={cn(
                'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                getPriorityStyles(task.priority)
              )}>
                {priorityConfig.label}
              </span>

              {/* Status Badge */}
              {task.status !== 'pending' && (
                <span className={cn(
                  'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                  statusConfig.color,
                  statusConfig.bgColor
                )}>
                  <span className="mr-1">{statusConfig.icon}</span>
                  {statusConfig.label}
                </span>
              )}
            </div>

            {/* Created Date */}
            <div className="flex items-center text-xs text-neutral-500">
              <Clock className="mr-1 h-3 w-3" />
              {formatDate(task.created_at)}
            </div>
          </div>

          {/* Quick Actions */}
          {!compact && (
            <div className="mt-3 flex items-center space-x-2">
              {task.status !== 'in_progress' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleStatusChange('in_progress')}
                  disabled={updateTaskStatus.isLoading}
                >
                  Start
                </Button>
              )}
              
              {task.status === 'in_progress' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleStatusChange('completed')}
                  disabled={updateTaskStatus.isLoading}
                >
                  Complete
                </Button>
              )}
              
              {task.status !== 'cancelled' && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleStatusChange('cancelled')}
                  disabled={updateTaskStatus.isLoading}
                  className="text-red-600 hover:bg-red-50"
                >
                  Cancel
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}
