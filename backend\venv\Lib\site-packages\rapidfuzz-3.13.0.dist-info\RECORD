rapidfuzz-3.13.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
rapidfuzz-3.13.0.dist-info/METADATA,sha256=6CyNhmCsM2LEm9036CuJI86yD7WkqReNufgvitJfabw,12285
rapidfuzz-3.13.0.dist-info/RECORD,,
rapidfuzz-3.13.0.dist-info/WHEEL,sha256=xwjTh-mFvAONxbW8av7jDNts8DLqzqCA-klxw1kaHPA,106
rapidfuzz-3.13.0.dist-info/entry_points.txt,sha256=j5_TXjLHLN9ULqtgOxy8V2w8yS2mzVnowf7vZO7y1uw,121
rapidfuzz-3.13.0.dist-info/licenses/LICENSE,sha256=80zN_9_1b8QXeFkV7t8YYB1UeXVd88RyssdUwpgC1oM,1092
rapidfuzz/__init__.py,sha256=QWtTPs_0laJ-SoQ9f1wPcffUAW7LFRgsNUBkhd_Ag4E,836
rapidfuzz/__init__.pyi,sha256=6NNoCowtWGbHBRHZ-KPumXXSpk2x4trPmStDWqk9Oug,201
rapidfuzz/__pycache__/__init__.cpython-313.pyc,,
rapidfuzz/__pycache__/_common_py.cpython-313.pyc,,
rapidfuzz/__pycache__/_feature_detector.cpython-313.pyc,,
rapidfuzz/__pycache__/_utils.cpython-313.pyc,,
rapidfuzz/__pycache__/fuzz.cpython-313.pyc,,
rapidfuzz/__pycache__/fuzz_py.cpython-313.pyc,,
rapidfuzz/__pycache__/process.cpython-313.pyc,,
rapidfuzz/__pycache__/process_cpp.cpython-313.pyc,,
rapidfuzz/__pycache__/process_py.cpython-313.pyc,,
rapidfuzz/__pycache__/utils.cpython-313.pyc,,
rapidfuzz/__pycache__/utils_py.cpython-313.pyc,,
rapidfuzz/__pyinstaller/__init__.py,sha256=eNjMZIHLEdeArOmqjRgXrhkxskvxgAQJoWcqg-ceqPs,132
rapidfuzz/__pyinstaller/__pycache__/__init__.cpython-313.pyc,,
rapidfuzz/__pyinstaller/__pycache__/test_rapidfuzz_packaging.cpython-313.pyc,,
rapidfuzz/__pyinstaller/test_rapidfuzz_packaging.py,sha256=iBssItCNdVZpr7JWDJt5z7dIUsydlruuPs2qeLpFg5k,1114
rapidfuzz/_common_py.py,sha256=xbcjDP1g8-Xk8IkHwIp_Cd4ep_K3HSwFcbhzSKb5jiE,1773
rapidfuzz/_feature_detector.py,sha256=vTnfTB-Jfljbrupxsvsa5AQcZAkGHg7FXBg0pI3_YVg,332
rapidfuzz/_feature_detector_cpp.cp313-win_amd64.pyd,sha256=3_UgiTbzd_T7z5tlfIubE7s-UAF4cX1EB0BamPSyTyA,35328
rapidfuzz/_utils.py,sha256=l-q6M5jMkJT_MqqYQak-k3lE2CAZruQRWiwvfT53_To,2342
rapidfuzz/distance/DamerauLevenshtein.py,sha256=LicvoaIad4CawQuKnG7UJPgNV7yL20lNXfj1CkLtmkg,4055
rapidfuzz/distance/DamerauLevenshtein.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/DamerauLevenshtein_py.py,sha256=-zdMM4vv70Dk5VHAhbjeB5TpOd6-Y0bFS0uYbSBB9Fk,6591
rapidfuzz/distance/Hamming.py,sha256=_dj9-1ubfqtKCA2h7JtzZa9NfhIKfbfStNerCuwvSGA,4392
rapidfuzz/distance/Hamming.pyi,sha256=TjoXt9lysM9-ZOedijCii50xGGdjGlz239LWx0wbYeY,2769
rapidfuzz/distance/Hamming_py.py,sha256=FAeokMCOcW3Ub8XlO6tsHWWpdJWSUKVbJCNZCnP-pmI,9081
rapidfuzz/distance/Indel.py,sha256=C-ZCyD3dOamKIvt1ltNDroKPSqbMh4zC4FaudhdvHko,4296
rapidfuzz/distance/Indel.pyi,sha256=xjs1sdft7AcbHOh9rp9pH0FamAf_TrN2qyWBdMXk-NE,2593
rapidfuzz/distance/Indel_py.py,sha256=oE-hmb92o-a5xYU3bj6UQtvUljJ4Cf6VYjXAIwStzWA,9935
rapidfuzz/distance/Jaro.py,sha256=kjB_r6CBVJfUjRiCiRkDrtdloVQ9ak2hUQB44nIlvN0,3575
rapidfuzz/distance/Jaro.pyi,sha256=IpjLgvh73jalJiMMNqwc6TSmk7U9yE_JXLP3Q5605GM,1908
rapidfuzz/distance/JaroWinkler.py,sha256=3RRZ4BNt9Rs4KALgQNYsp8HCBZ4BdMxMOcirnMGwxvo,3831
rapidfuzz/distance/JaroWinkler.pyi,sha256=5XC-Y5fYFnmINM0Dw29BgRUvzFfnG07HVruCrwL4MF8,2164
rapidfuzz/distance/JaroWinkler_py.py,sha256=CJA9ImEQPGqRToXgjejdUES93L5JRZf9gGkbVuX6AbU,6481
rapidfuzz/distance/Jaro_py.py,sha256=1mexVsftTGRzhjctfUHuaG3D8M8T9oADuO9CqKoileU,7345
rapidfuzz/distance/LCSseq.py,sha256=tg62FG8PzPxYGdhx428xUfcd8cgEalB_hmqxc5K-Zcw,4392
rapidfuzz/distance/LCSseq.pyi,sha256=xjs1sdft7AcbHOh9rp9pH0FamAf_TrN2qyWBdMXk-NE,2593
rapidfuzz/distance/LCSseq_py.py,sha256=mH3JXKvXllXX3Ts6daGfdDKGWo5V1xHkI9RyWGVVw5E,11504
rapidfuzz/distance/Levenshtein.py,sha256=UJGLcs9dXL25kOjbi6TOCUHXf6uGMlGTWSotxRXf3JU,4584
rapidfuzz/distance/Levenshtein.pyi,sha256=7C-nWn_yEORoj-Ioow0BsmaLnpKH7ej_5smykFtwcnM,3678
rapidfuzz/distance/Levenshtein_py.py,sha256=f4gag2BCACSGXPVe0gnGnYf5CDLAdyqhvf5cUl9ptRU,17103
rapidfuzz/distance/OSA.py,sha256=DzkSlRrEThCf8zc9OCI5tgNqGAzusWcn5cYE2RAVK64,3543
rapidfuzz/distance/OSA.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/OSA_py.py,sha256=BGD49wAjNbA5xqhrUID4vkA7yaeJ3jXljsE60SWbEig,6251
rapidfuzz/distance/Postfix.py,sha256=QoTlbLgjj61CDM_rycFDoap3gARRWXOu6vwIw41Y0QE,3671
rapidfuzz/distance/Postfix.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/Postfix_py.py,sha256=ywWO0M5swCWkjZqjp2wKMEZ7_GV8Z91IRMe8_zZGeaQ,5039
rapidfuzz/distance/Prefix.py,sha256=2Iu577WnaYCHCQd8it76gDk60QxRdlFo0cQBItCZzWw,3639
rapidfuzz/distance/Prefix.pyi,sha256=bMR0Hm2512jXdcBXxTfPQOZwnJonCIY8X2RJfkXsEzg,1904
rapidfuzz/distance/Prefix_py.py,sha256=mHtW3wRD0jol76NnDOgIALMvslINtGJJ9ipZ713sldQ,5015
rapidfuzz/distance/__init__.py,sha256=4DnHc3o3Bs6xUyFJTQKqPgenKSuk3Wx80IcUuzRgp0U,621
rapidfuzz/distance/__init__.pyi,sha256=wX594ohPZ4gASmvKpbovDWH2BB9IcVojRqtF0yPjzvg,571
rapidfuzz/distance/__pycache__/DamerauLevenshtein.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/DamerauLevenshtein_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Hamming.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Hamming_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Indel.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Indel_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Jaro.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/JaroWinkler_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Jaro_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/LCSseq.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/LCSseq_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Levenshtein_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/OSA.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/OSA_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Postfix.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Postfix_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Prefix.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/Prefix_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/__init__.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/_initialize.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/_initialize_py.cpython-313.pyc,,
rapidfuzz/distance/__pycache__/metrics_py.cpython-313.pyc,,
rapidfuzz/distance/_initialize.py,sha256=UKwKITEVDTvj7MiWNsmPJBDN7xgowVwlzKllEjDzpoA,3074
rapidfuzz/distance/_initialize.pyi,sha256=N7ooznQtrQhZKNZ1UGtjUyf6LiP_vbtSCWFa49K9fJA,3998
rapidfuzz/distance/_initialize_cpp.cp313-win_amd64.pyd,sha256=yygepDeGVH23Xi7HsBMJ58KuSc3SCRKjupgFb8cq6JM,273408
rapidfuzz/distance/_initialize_py.py,sha256=I_z-54avD84EE1HA7KjT8and4GHOQI-o2tK9ZG6eYsI,26320
rapidfuzz/distance/metrics_cpp.cp313-win_amd64.pyd,sha256=oiuUf5Yqu0W1njfn_RXVbOaRhhqSawbZ_P5soPsUdJM,1592832
rapidfuzz/distance/metrics_cpp_avx2.cp313-win_amd64.pyd,sha256=Zf1RQhhH1fQHGKM_zHLtysn9gKc9s4buYlLw7zUFcD0,1602048
rapidfuzz/distance/metrics_py.py,sha256=qtm2UeWOXEAmVUPnxsSE2COrd9KuEArOKTlqQI2qqtk,9037
rapidfuzz/fuzz.py,sha256=gkOM8b_kJ5yaq1er_sAqXJP6wbZJon9msV2L6EwGS24,4625
rapidfuzz/fuzz.pyi,sha256=8I3AJtX50VlobaAgFtqiXg1-pbzaRA9dFSKezv7zgUU,4811
rapidfuzz/fuzz_cpp.cp313-win_amd64.pyd,sha256=_cWUChWb55B7YJaSyz2qGopZuFQUW14ODH5Nnao20lA,922624
rapidfuzz/fuzz_cpp_avx2.cp313-win_amd64.pyd,sha256=3kVCoM3u_LeGNj0IG6p_YejY1_WrTs7RCxHWR9pVFqQ,925184
rapidfuzz/fuzz_py.py,sha256=JVwHUd6dXBsqrszsHV6mPd6b1k3BPaeFVSjQ4TVAE7I,25825
rapidfuzz/process.py,sha256=JylT-tYBhwP566p0zO-O2ZT0p-0kW3cCUgbnCCOoVcQ,2672
rapidfuzz/process.pyi,sha256=uU6KxGPEbPEwqISyiG2fFZTAAZlpEns74SKQle5CTUQ,17141
rapidfuzz/process_cpp.py,sha256=ee6UAEEQ5DTanlLTcHA6g3iOGgjmhAdoDW9gUpmoF74,2530
rapidfuzz/process_cpp_impl.cp313-win_amd64.pyd,sha256=nblH0dxxPL8F9W8bRuL8gkjV4mY_w71NjjlAdGdDbvw,567808
rapidfuzz/process_py.py,sha256=B1Cwjpzjta2jMPPr20HhEZOeNryHq5M4AH3k55iPdHM,26646
rapidfuzz/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rapidfuzz/utils.py,sha256=7rs2NCCSL2_mandvxSEkcly5WWb0RR4gxKOdYZJiavE,1910
rapidfuzz/utils.pyi,sha256=rz2lxhxQu-Bm6gPxBDjwBbNs2oBBjArP6PEYlWcaE5Q,304
rapidfuzz/utils_cpp.cp313-win_amd64.pyd,sha256=Km06ogfPH4S6oAMNvOv4xBzswltHSrganmtTPTsgDds,165376
rapidfuzz/utils_py.py,sha256=oP6xgOsopkR0NhNjf3u6x55gw03LUMJ1zh2FYIsxno4,622
