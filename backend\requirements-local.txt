# FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database (SQLite for local development)
sqlalchemy==2.0.23
aiosqlite==0.19.0
alembic==1.12.1

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Text processing and similarity
rapidfuzz==3.5.2
thefuzz==0.20.0
python-Levenshtein==0.23.0

# Utilities
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0
httpx==0.25.2
aiofiles==23.2.1

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1

# WebSocket support
websockets==12.0
