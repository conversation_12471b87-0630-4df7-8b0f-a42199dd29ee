from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any
from ..core.config import settings
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/settings", tags=["settings"])


class DuplicateDetectionSettings(BaseModel):
    """Duplicate detection settings model"""
    enabled: bool = Field(default=True, description="Enable/disable duplicate detection")
    threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Similarity threshold for duplicate detection")
    max_suggestions: int = Field(default=5, ge=1, le=20, description="Maximum number of duplicate suggestions to show")
    auto_merge_threshold: float = Field(default=0.95, ge=0.0, le=1.0, description="Threshold for automatic merging")
    show_reasons: bool = Field(default=True, description="Show reasons for duplicate matches")
    context_boost: float = Field(default=0.05, ge=0.0, le=0.2, description="Boost score for same context matches")


class AppSettings(BaseModel):
    """Application settings model"""
    duplicate_detection: DuplicateDetectionSettings


class SettingsUpdateRequest(BaseModel):
    """Settings update request model"""
    duplicate_detection: DuplicateDetectionSettings


@router.get("/", response_model=AppSettings)
async def get_settings():
    """
    Get current application settings
    """
    try:
        return AppSettings(
            duplicate_detection=DuplicateDetectionSettings(
                enabled=settings.duplicate_detection_enabled,
                threshold=settings.duplicate_detection_threshold,
                max_suggestions=settings.max_duplicate_suggestions,
                auto_merge_threshold=settings.duplicate_detection_auto_merge_threshold,
                show_reasons=settings.duplicate_detection_show_reasons,
                context_boost=settings.duplicate_detection_context_boost
            )
        )
    except Exception as e:
        logger.error(f"Error getting settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get settings")


@router.patch("/", response_model=AppSettings)
async def update_settings(settings_update: SettingsUpdateRequest):
    """
    Update application settings
    """
    try:
        # Update duplicate detection settings
        dd_settings = settings_update.duplicate_detection
        
        settings.duplicate_detection_enabled = dd_settings.enabled
        settings.duplicate_detection_threshold = dd_settings.threshold
        settings.max_duplicate_suggestions = dd_settings.max_suggestions
        settings.duplicate_detection_auto_merge_threshold = dd_settings.auto_merge_threshold
        settings.duplicate_detection_show_reasons = dd_settings.show_reasons
        settings.duplicate_detection_context_boost = dd_settings.context_boost
        
        logger.info("Settings updated successfully")
        
        # Return updated settings
        return AppSettings(
            duplicate_detection=DuplicateDetectionSettings(
                enabled=settings.duplicate_detection_enabled,
                threshold=settings.duplicate_detection_threshold,
                max_suggestions=settings.max_duplicate_suggestions,
                auto_merge_threshold=settings.duplicate_detection_auto_merge_threshold,
                show_reasons=settings.duplicate_detection_show_reasons,
                context_boost=settings.duplicate_detection_context_boost
            )
        )
    except Exception as e:
        logger.error(f"Error updating settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to update settings")


@router.post("/reset")
async def reset_settings():
    """
    Reset settings to default values
    """
    try:
        # Reset to default values
        settings.duplicate_detection_enabled = True
        settings.duplicate_detection_threshold = 0.7
        settings.max_duplicate_suggestions = 5
        settings.duplicate_detection_auto_merge_threshold = 0.95
        settings.duplicate_detection_show_reasons = True
        settings.duplicate_detection_context_boost = 0.05
        
        logger.info("Settings reset to defaults")
        
        return {"message": "Settings reset to default values"}
    except Exception as e:
        logger.error(f"Error resetting settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to reset settings")


@router.get("/duplicate-detection", response_model=DuplicateDetectionSettings)
async def get_duplicate_detection_settings():
    """
    Get duplicate detection settings specifically
    """
    try:
        return DuplicateDetectionSettings(
            enabled=settings.duplicate_detection_enabled,
            threshold=settings.duplicate_detection_threshold,
            max_suggestions=settings.max_duplicate_suggestions,
            auto_merge_threshold=settings.duplicate_detection_auto_merge_threshold,
            show_reasons=settings.duplicate_detection_show_reasons,
            context_boost=settings.duplicate_detection_context_boost
        )
    except Exception as e:
        logger.error(f"Error getting duplicate detection settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to get duplicate detection settings")


@router.patch("/duplicate-detection", response_model=DuplicateDetectionSettings)
async def update_duplicate_detection_settings(dd_settings: DuplicateDetectionSettings):
    """
    Update duplicate detection settings specifically
    """
    try:
        settings.duplicate_detection_enabled = dd_settings.enabled
        settings.duplicate_detection_threshold = dd_settings.threshold
        settings.max_duplicate_suggestions = dd_settings.max_suggestions
        settings.duplicate_detection_auto_merge_threshold = dd_settings.auto_merge_threshold
        settings.duplicate_detection_show_reasons = dd_settings.show_reasons
        settings.duplicate_detection_context_boost = dd_settings.context_boost
        
        logger.info("Duplicate detection settings updated successfully")
        
        return DuplicateDetectionSettings(
            enabled=settings.duplicate_detection_enabled,
            threshold=settings.duplicate_detection_threshold,
            max_suggestions=settings.max_duplicate_suggestions,
            auto_merge_threshold=settings.duplicate_detection_auto_merge_threshold,
            show_reasons=settings.duplicate_detection_show_reasons,
            context_boost=settings.duplicate_detection_context_boost
        )
    except Exception as e:
        logger.error(f"Error updating duplicate detection settings: {e}")
        raise HTTPException(status_code=500, detail="Failed to update duplicate detection settings")
