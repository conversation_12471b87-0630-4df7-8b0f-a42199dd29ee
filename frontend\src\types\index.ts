// Type definitions for the ADHD Productivity Dashboard

export interface Task {
  id: string;
  title: string;
  description?: string;
  context: TaskContext;
  priority: TaskPriority;
  status: TaskStatus;
  completed: boolean;
  created_at: string;
  updated_at?: string;
  source_type?: string;
}

export type TaskContext = 'work';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

export interface TaskCreateRequest {
  title: string;
  description?: string;
  context?: TaskContext;
  priority?: TaskPriority;
  check_duplicates?: boolean;
}

export interface TaskCreateResponse {
  status: 'created' | 'duplicate_warning';
  task?: Task;
  duplicates?: DuplicateMatch[];
  message?: string;
  total_matches?: number;
  highest_similarity?: number;
}

export interface DuplicateMatch {
  id: string;
  title: string;
  description?: string;
  context?: string;
  status?: string;
  priority?: string;
  created_at?: string;
  similarity_score: number;
  match_type: string;
  confidence: number;
  reasons: string[];
}

// Legacy interface for backward compatibility
export interface DuplicateTask {
  id: string;
  title: string;
  similarity: number;
  context?: string;
  status?: string;
}

export interface TaskStats {
  total_tasks: number;
  pending: number;
  in_progress: number;
  completed: number;
  context: string;
}

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// Widget types for dashboard
export interface Widget {
  id: string;
  type: WidgetType;
  title: string;
  position: WidgetPosition;
  size: WidgetSize;
  config?: Record<string, any>;
}

export type WidgetType = 
  | 'task-list' 
  | 'task-stats' 
  | 'quick-add' 
  | 'context-switcher'
  | 'recent-tasks'
  | 'calendar'
  | 'notes';

export interface WidgetPosition {
  x: number;
  y: number;
}

export interface WidgetSize {
  width: number;
  height: number;
}

// Dashboard layout
export interface DashboardLayout {
  widgets: Widget[];
  contexts: TaskContext[];
  activeContext: TaskContext;
}

// User preferences
export interface UserPreferences {
  theme: 'light' | 'dark';
  duplicateThreshold: number;
  defaultContext: TaskContext;
  enabledContexts: TaskContext[];
  widgetSettings: Record<string, any>;
}
