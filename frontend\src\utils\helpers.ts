import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format, formatDistanceToNow, isToday, isYesterday, parseISO } from 'date-fns';
import type { Task, TaskContext, TaskPriority, TaskStatus } from '@/types';

// Utility function for combining Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format dates in an ADHD-friendly way
export function formatDate(dateString: string): string {
  try {
    const date = parseISO(dateString);
    
    if (isToday(date)) {
      return `Today at ${format(date, 'h:mm a')}`;
    }
    
    if (isYesterday(date)) {
      return `Yesterday at ${format(date, 'h:mm a')}`;
    }
    
    // For dates within the last week, show relative time
    const daysAgo = Math.floor((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
    if (daysAgo <= 7) {
      return formatDistanceToNow(date, { addSuffix: true });
    }
    
    // For older dates, show full date
    return format(date, 'MMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
}

// Get context-specific styling
export function getContextStyles(context: TaskContext) {
  const styles = {
    work: 'border-l-work bg-work/5 text-work',
  };

  return styles[context] || styles.work;
}

// Get priority-specific styling
export function getPriorityStyles(priority: TaskPriority) {
  const styles = {
    low: 'border-neutral-200 bg-neutral-50 text-neutral-600',
    medium: 'border-primary-200 bg-primary-50 text-primary-600',
    high: 'border-accent-200 bg-accent-50 text-accent-600',
    urgent: 'border-red-200 bg-red-50 text-red-600',
  };
  
  return styles[priority] || styles.medium;
}

// Get status-specific styling
export function getStatusStyles(status: TaskStatus) {
  const styles = {
    pending: 'border-neutral-200 bg-neutral-50 text-neutral-600',
    in_progress: 'border-primary-200 bg-primary-50 text-primary-600',
    completed: 'border-secondary-200 bg-secondary-50 text-secondary-600',
    cancelled: 'border-red-200 bg-red-50 text-red-600',
  };
  
  return styles[status] || styles.pending;
}

// Sort tasks by priority and creation date
export function sortTasks(tasks: Task[]): Task[] {
  const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
  
  return [...tasks].sort((a, b) => {
    // First sort by completion status (incomplete tasks first)
    if (a.completed !== b.completed) {
      return a.completed ? 1 : -1;
    }
    
    // Then by priority
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) {
      return priorityDiff;
    }
    
    // Finally by creation date (newest first)
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });
}

// Filter tasks by search query
export function filterTasks(tasks: Task[], query: string): Task[] {
  if (!query.trim()) return tasks;
  
  const searchTerm = query.toLowerCase();
  
  return tasks.filter(task => 
    task.title.toLowerCase().includes(searchTerm) ||
    task.description?.toLowerCase().includes(searchTerm) ||
    task.context.toLowerCase().includes(searchTerm) ||
    task.priority.toLowerCase().includes(searchTerm) ||
    task.status.toLowerCase().includes(searchTerm)
  );
}

// Generate a random ID for widgets
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Debounce function for search and other inputs
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Validate task title for ADHD-friendly constraints
export function validateTaskTitle(title: string): { isValid: boolean; message?: string } {
  if (!title.trim()) {
    return { isValid: false, message: 'Task title is required' };
  }
  
  if (title.length > 100) {
    return { isValid: false, message: 'Task title should be under 100 characters for clarity' };
  }
  
  if (title.length < 3) {
    return { isValid: false, message: 'Task title should be at least 3 characters' };
  }
  
  return { isValid: true };
}

// Get task completion percentage for a list of tasks
export function getCompletionPercentage(tasks: Task[]): number {
  if (tasks.length === 0) return 0;
  
  const completedTasks = tasks.filter(task => task.completed).length;
  return Math.round((completedTasks / tasks.length) * 100);
}

// Group tasks by context
export function groupTasksByContext(tasks: Task[]): Record<TaskContext, Task[]> {
  return tasks.reduce((groups, task) => {
    const context = task.context;
    if (!groups[context]) {
      groups[context] = [];
    }
    groups[context].push(task);
    return groups;
  }, {} as Record<TaskContext, Task[]>);
}
