import React, { useState } from 'react';
import { Plus, AlertTriangle, Check } from 'lucide-react';
import { <PERSON>, CardHeader, CardT<PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Textarea } from '@/components/ui/Input';
import { DuplicateDetectionModal } from './DuplicateDetectionModal';
import { useCreateTask, useCheckDuplicates } from '@/hooks/useTasks';
import { validateTaskTitle, cn } from '@/utils/helpers';
import { CONTEXTS, PRIORITIES } from '@/utils/constants';
import type { TaskContext, TaskPriority, TaskCreateRequest, DuplicateMatch } from '@/types';
import toast from 'react-hot-toast';

export function QuickAddTask() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [formData, setFormData] = useState<TaskCreateRequest>({
    title: '',
    description: '',
    context: 'work',
    priority: 'medium',
    check_duplicates: true,
  });
  const [duplicates, setDuplicates] = useState<DuplicateMatch[]>([]);
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);

  const createTask = useCreateTask();
  const checkDuplicates = useCheckDuplicates();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateTaskTitle(formData.title);
    if (!validation.isValid) {
      toast.error(validation.message);
      return;
    }

    try {
      const response = await createTask.mutateAsync(formData);
      
      if (response.status === 'duplicate_warning') {
        setDuplicates(response.duplicates || []);
        setShowDuplicateModal(true);
        return;
      }

      // Task created successfully
      setFormData({
        title: '',
        description: '',
        context: 'work',
        priority: 'medium',
        check_duplicates: true,
      });
      setIsExpanded(false);
      setShowDuplicateModal(false);
      setDuplicates([]);
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  const handleCreateAnyway = async () => {
    try {
      await createTask.mutateAsync({
        ...formData,
        check_duplicates: false,
      });
      
      setFormData({
        title: '',
        description: '',
        context: 'work',
        priority: 'medium',
        check_duplicates: true,
      });
      setIsExpanded(false);
      setShowDuplicateModal(false);
      setDuplicates([]);
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    setFormData(prev => ({ ...prev, title }));
    
    // Reset duplicate modal when title changes
    if (showDuplicateModal) {
      setShowDuplicateModal(false);
      setDuplicates([]);
    }
  };

  if (!isExpanded) {
    return (
      <Card hover className="cursor-pointer" onClick={() => setIsExpanded(true)}>
        <div className="flex items-center justify-center p-6 text-neutral-500 hover:text-neutral-700">
          <Plus className="mr-2 h-5 w-5" />
          <span className="font-medium">Add a new task...</span>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New Task</CardTitle>
      </CardHeader>
      
      <CardContent>

        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Task Title"
            placeholder="What needs to be done?"
            value={formData.title}
            onChange={handleTitleChange}
            required
            autoFocus
          />

          <Textarea
            label="Description (Optional)"
            placeholder="Add more details..."
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">
                Context
              </label>
              <select
                value={formData.context}
                onChange={(e) => setFormData(prev => ({ ...prev, context: e.target.value as TaskContext }))}
                className="w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                {Object.entries(CONTEXTS).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">
                Priority
              </label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as TaskPriority }))}
                className="w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                {Object.entries(PRIORITIES).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                setIsExpanded(false);
                setShowDuplicateModal(false);
                setDuplicates([]);
                setFormData({
                  title: '',
                  description: '',
                  context: 'work',
                  priority: 'medium',
                  check_duplicates: true,
                });
              }}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              loading={createTask.isLoading}
              disabled={!formData.title.trim()}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Task
            </Button>
          </div>
        </form>
      </CardContent>

      {/* Duplicate Detection Modal */}
      <DuplicateDetectionModal
        isOpen={showDuplicateModal}
        duplicates={duplicates}
        newTaskTitle={formData.title}
        onClose={() => {
          setShowDuplicateModal(false);
          setDuplicates([]);
        }}
        onCreateAnyway={handleCreateAnyway}
        isLoading={createTask.isLoading}
      />
    </Card>
  );
}
