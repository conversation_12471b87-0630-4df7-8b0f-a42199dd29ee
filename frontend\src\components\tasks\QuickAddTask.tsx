import React, { useState } from 'react';
import { Plus, AlertTriangle, Check } from 'lucide-react';
import { <PERSON>, <PERSON>H<PERSON>er, CardT<PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Textarea } from '@/components/ui/Input';
import { useCreateTask, useCheckDuplicates } from '@/hooks/useTasks';
import { validateTaskTitle, cn } from '@/utils/helpers';
import { CONTEXTS, PRIORITIES } from '@/utils/constants';
import type { TaskContext, TaskPriority, TaskCreateRequest, DuplicateTask } from '@/types';
import toast from 'react-hot-toast';

export function QuickAddTask() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [formData, setFormData] = useState<TaskCreateRequest>({
    title: '',
    description: '',
    context: 'work',
    priority: 'medium',
    check_duplicates: true,
  });
  const [duplicates, setDuplicates] = useState<DuplicateTask[]>([]);
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);

  const createTask = useCreateTask();
  const checkDuplicates = useCheckDuplicates();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateTaskTitle(formData.title);
    if (!validation.isValid) {
      toast.error(validation.message);
      return;
    }

    try {
      const response = await createTask.mutateAsync(formData);
      
      if (response.status === 'duplicate_warning') {
        setDuplicates(response.duplicates || []);
        setShowDuplicateWarning(true);
        return;
      }

      // Task created successfully
      setFormData({
        title: '',
        description: '',
        context: 'work',
        priority: 'medium',
        check_duplicates: true,
      });
      setIsExpanded(false);
      setShowDuplicateWarning(false);
      setDuplicates([]);
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  const handleCreateAnyway = async () => {
    try {
      await createTask.mutateAsync({
        ...formData,
        check_duplicates: false,
      });
      
      setFormData({
        title: '',
        description: '',
        context: 'work',
        priority: 'medium',
        check_duplicates: true,
      });
      setIsExpanded(false);
      setShowDuplicateWarning(false);
      setDuplicates([]);
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    setFormData(prev => ({ ...prev, title }));
    
    // Reset duplicate warning when title changes
    if (showDuplicateWarning) {
      setShowDuplicateWarning(false);
      setDuplicates([]);
    }
  };

  if (!isExpanded) {
    return (
      <Card hover className="cursor-pointer" onClick={() => setIsExpanded(true)}>
        <div className="flex items-center justify-center p-6 text-neutral-500 hover:text-neutral-700">
          <Plus className="mr-2 h-5 w-5" />
          <span className="font-medium">Add a new task...</span>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New Task</CardTitle>
      </CardHeader>
      
      <CardContent>
        {showDuplicateWarning && duplicates.length > 0 && (
          <div className="mb-4 rounded-lg border border-accent-200 bg-accent-50 p-4">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="mt-0.5 h-5 w-5 text-accent-600" />
              <div className="flex-1">
                <h4 className="font-medium text-accent-800">Similar tasks found</h4>
                <p className="mt-1 text-sm text-accent-700">
                  We found {duplicates.length} similar task{duplicates.length > 1 ? 's' : ''}. 
                  Review them below or create anyway.
                </p>
                
                <div className="mt-3 space-y-2">
                  {duplicates.map((duplicate) => (
                    <div
                      key={duplicate.id}
                      className="rounded-lg border border-accent-200 bg-white p-3"
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-neutral-900">{duplicate.title}</span>
                        <span className="text-sm text-accent-600">
                          {Math.round(duplicate.similarity * 100)}% similar
                        </span>
                      </div>
                      {duplicate.context && (
                        <span className="mt-1 inline-block text-xs text-neutral-500">
                          {CONTEXTS[duplicate.context as TaskContext]?.label}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
                
                <div className="mt-4 flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setShowDuplicateWarning(false);
                      setDuplicates([]);
                    }}
                  >
                    Review & Edit
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleCreateAnyway}
                    loading={createTask.isLoading}
                  >
                    Create Anyway
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Task Title"
            placeholder="What needs to be done?"
            value={formData.title}
            onChange={handleTitleChange}
            required
            autoFocus
          />

          <Textarea
            label="Description (Optional)"
            placeholder="Add more details..."
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={3}
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">
                Context
              </label>
              <select
                value={formData.context}
                onChange={(e) => setFormData(prev => ({ ...prev, context: e.target.value as TaskContext }))}
                className="w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                {Object.entries(CONTEXTS).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.icon} {config.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">
                Priority
              </label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as TaskPriority }))}
                className="w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              >
                {Object.entries(PRIORITIES).map(([key, config]) => (
                  <option key={key} value={key}>
                    {config.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex items-center justify-between pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                setIsExpanded(false);
                setShowDuplicateWarning(false);
                setDuplicates([]);
                setFormData({
                  title: '',
                  description: '',
                  context: 'work',
                  priority: 'medium',
                  check_duplicates: true,
                });
              }}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              loading={createTask.isLoading}
              disabled={!formData.title.trim()}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Task
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
