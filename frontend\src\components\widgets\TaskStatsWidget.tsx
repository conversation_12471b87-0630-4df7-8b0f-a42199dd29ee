import React from 'react';
import { BarChart3, TrendingU<PERSON>, <PERSON>, CheckCircle2 } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { useTaskStats } from '@/hooks/useTasks';
import { cn } from '@/utils/helpers';
import type { TaskContext } from '@/types';

interface TaskStatsWidgetProps {
  context?: TaskContext;
}

export function TaskStatsWidget({ context }: TaskStatsWidgetProps) {
  const { data: stats, isLoading, error } = useTaskStats(context);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            Task Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-neutral-200 rounded"></div>
            <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
            <div className="h-4 bg-neutral-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            Task Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-neutral-500">Unable to load task statistics</p>
        </CardContent>
      </Card>
    );
  }

  const completionRate = stats.total_tasks > 0 
    ? Math.round((stats.completed / stats.total_tasks) * 100) 
    : 0;

  const statItems = [
    {
      label: 'Total Tasks',
      value: stats.total_tasks,
      icon: BarChart3,
      color: 'text-neutral-600',
      bgColor: 'bg-neutral-100',
    },
    {
      label: 'Pending',
      value: stats.pending,
      icon: Clock,
      color: 'text-neutral-600',
      bgColor: 'bg-neutral-100',
    },
    {
      label: 'In Progress',
      value: stats.in_progress,
      icon: TrendingUp,
      color: 'text-primary-600',
      bgColor: 'bg-primary-100',
    },
    {
      label: 'Completed',
      value: stats.completed,
      icon: CheckCircle2,
      color: 'text-secondary-600',
      bgColor: 'bg-secondary-100',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart3 className="mr-2 h-5 w-5" />
          Task Overview
          {context && (
            <span className="ml-2 text-sm font-normal text-neutral-500">
              ({context.replace('_', ' ')})
            </span>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        {stats.total_tasks === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto h-12 w-12 rounded-full bg-neutral-100 flex items-center justify-center mb-4">
              <BarChart3 className="h-6 w-6 text-neutral-400" />
            </div>
            <p className="text-neutral-500">No tasks yet</p>
            <p className="text-sm text-neutral-400 mt-1">
              Create your first task to get started!
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Completion Rate */}
            <div className="text-center">
              <div className="text-3xl font-bold text-neutral-900 mb-1">
                {completionRate}%
              </div>
              <p className="text-sm text-neutral-500">Completion Rate</p>
              
              {/* Progress Bar */}
              <div className="mt-3 w-full bg-neutral-200 rounded-full h-2">
                <div
                  className="bg-secondary-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${completionRate}%` }}
                />
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-3">
              {statItems.map((item) => (
                <div
                  key={item.label}
                  className="rounded-lg border border-neutral-200 p-3 text-center"
                >
                  <div className={cn(
                    'mx-auto h-8 w-8 rounded-full flex items-center justify-center mb-2',
                    item.bgColor
                  )}>
                    <item.icon className={cn('h-4 w-4', item.color)} />
                  </div>
                  <div className="text-lg font-semibold text-neutral-900">
                    {item.value}
                  </div>
                  <div className="text-xs text-neutral-500">
                    {item.label}
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Insights */}
            <div className="pt-2 border-t border-neutral-100">
              <div className="text-xs text-neutral-500 space-y-1">
                {stats.in_progress > 0 && (
                  <p>🔄 {stats.in_progress} task{stats.in_progress > 1 ? 's' : ''} in progress</p>
                )}
                {stats.pending > 0 && (
                  <p>⏳ {stats.pending} task{stats.pending > 1 ? 's' : ''} waiting to start</p>
                )}
                {completionRate >= 80 && stats.total_tasks > 0 && (
                  <p>🎉 Great progress! You're doing amazing!</p>
                )}
                {completionRate < 50 && stats.total_tasks > 3 && (
                  <p>💪 Keep going! Small steps lead to big wins!</p>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
