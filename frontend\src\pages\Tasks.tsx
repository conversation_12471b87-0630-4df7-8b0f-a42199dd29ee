import React, { useState } from 'react';
import { Search, Filter, SortAsc } from 'lucide-react';
import { TaskCard } from '@/components/tasks/TaskCard';
import { QuickAddTask } from '@/components/tasks/QuickAddTask';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { useTasks } from '@/hooks/useTasks';
import { sortTasks, filterTasks, cn } from '@/utils/helpers';
import { CONTEXTS, STATUSES } from '@/utils/constants';
import type { TaskContext, TaskStatus } from '@/types';

export function Tasks() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeContext, setActiveContext] = useState<TaskContext | 'all'>('all');
  const [activeStatus, setActiveStatus] = useState<TaskStatus | 'all'>('all');
  const [showCompleted, setShowCompleted] = useState(true);

  const { data: allTasks = [], isLoading, error } = useTasks();

  // Filter tasks based on current filters
  const filteredTasks = React.useMemo(() => {
    let tasks = allTasks;

    // Filter by context
    if (activeContext !== 'all') {
      tasks = tasks.filter(task => task.context === activeContext);
    }

    // Filter by status
    if (activeStatus !== 'all') {
      tasks = tasks.filter(task => task.status === activeStatus);
    }

    // Filter by completion status
    if (!showCompleted) {
      tasks = tasks.filter(task => !task.completed);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      tasks = filterTasks(tasks, searchQuery);
    }

    return sortTasks(tasks);
  }, [allTasks, activeContext, activeStatus, showCompleted, searchQuery]);

  const taskCounts = React.useMemo(() => {
    return {
      all: allTasks.length,
      pending: allTasks.filter(t => t.status === 'pending').length,
      in_progress: allTasks.filter(t => t.status === 'in_progress').length,
      completed: allTasks.filter(t => t.status === 'completed').length,
      cancelled: allTasks.filter(t => t.status === 'cancelled').length,
    };
  }, [allTasks]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-neutral-900">All Tasks</h1>
        <p className="mt-1 text-neutral-600">
          Manage and organize your tasks across all contexts
        </p>
      </div>

      {/* Quick Add */}
      <QuickAddTask />

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4 space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
            <Input
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Context Filter */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Context
            </label>
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant={activeContext === 'all' ? 'primary' : 'outline'}
                onClick={() => setActiveContext('all')}
              >
                All ({allTasks.length})
              </Button>
              
              {Object.entries(CONTEXTS).map(([key, config]) => {
                const count = allTasks.filter(t => t.context === key).length;
                return (
                  <Button
                    key={key}
                    size="sm"
                    variant={activeContext === key ? 'primary' : 'outline'}
                    onClick={() => setActiveContext(key as TaskContext)}
                    className="flex items-center space-x-1"
                  >
                    <span>{config.icon}</span>
                    <span>{config.label} ({count})</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-neutral-700 mb-2">
              Status
            </label>
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant={activeStatus === 'all' ? 'primary' : 'outline'}
                onClick={() => setActiveStatus('all')}
              >
                All
              </Button>
              
              {Object.entries(STATUSES).map(([key, config]) => {
                const count = taskCounts[key as keyof typeof taskCounts] || 0;
                return (
                  <Button
                    key={key}
                    size="sm"
                    variant={activeStatus === key ? 'primary' : 'outline'}
                    onClick={() => setActiveStatus(key as TaskStatus)}
                    className="flex items-center space-x-1"
                  >
                    <span>{config.icon}</span>
                    <span>{config.label} ({count})</span>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Additional Options */}
          <div className="flex items-center justify-between pt-2 border-t border-neutral-100">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showCompleted}
                onChange={(e) => setShowCompleted(e.target.checked)}
                className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-neutral-700">Show completed tasks</span>
            </label>
            
            <div className="text-sm text-neutral-500">
              Showing {filteredTasks.length} of {allTasks.length} tasks
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Task List */}
      <div>
        {isLoading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-32 bg-neutral-200 rounded-lg"></div>
              </div>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-red-600">Failed to load tasks</p>
              <p className="text-sm text-neutral-500 mt-1">
                Please check your connection and try again
              </p>
            </CardContent>
          </Card>
        ) : filteredTasks.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="mx-auto h-12 w-12 rounded-full bg-neutral-100 flex items-center justify-center mb-4">
                <Filter className="h-6 w-6 text-neutral-400" />
              </div>
              <p className="text-neutral-500">
                {searchQuery.trim() 
                  ? `No tasks found for "${searchQuery}"`
                  : 'No tasks match your current filters'
                }
              </p>
              <p className="text-sm text-neutral-400 mt-1">
                Try adjusting your filters or create a new task
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredTasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
