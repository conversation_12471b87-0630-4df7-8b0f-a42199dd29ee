from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import logging
import time

from .core.config import settings
from .core.database import init_db, close_db
from .api import tasks

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting Make Work Easy Dashboard API")
    await init_db()
    logger.info("Database initialized")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Make Work Easy Dashboard API")
    await close_db()
    logger.info("Database connections closed")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="A locally-hosted productivity dashboard designed to make work easy",
    lifespan=lifespan,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# Add security middleware
app.add_middleware(
    TrustedHostMiddleware, 
    allowed_hosts=["localhost", "127.0.0.1", "*.local"]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)


# Add request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "type": "internal_error"
        }
    )


# Include API routers
app.include_router(tasks.router)


# Health check endpoints
@app.get("/health")
async def health_check():
    """Basic health check"""
    return {
        "status": "ok", 
        "service": "productivity-dashboard",
        "version": settings.version
    }


@app.get("/health/detailed")
async def detailed_health_check():
    """Detailed health check with system info"""
    return {
        "status": "ok",
        "service": "productivity-dashboard",
        "version": settings.version,
        "debug": settings.debug,
        "database_url": settings.database_url.split("@")[0] + "@***",  # Hide credentials
        "redis_url": settings.redis_url.split("@")[0] + "@***" if "@" in settings.redis_url else settings.redis_url,
    }


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Make Work Easy Dashboard API",
        "version": settings.version,
        "docs": "/docs" if settings.debug else "Documentation disabled in production",
        "health": "/health"
    }


# API info endpoint
@app.get("/api/info")
async def api_info():
    """API information and available endpoints"""
    return {
        "name": settings.app_name,
        "version": settings.version,
        "endpoints": {
            "tasks": "/api/tasks",
            "health": "/health",
            "docs": "/docs" if settings.debug else None
        },
        "features": [
            "Task management with duplicate detection",
            "Work-focused design principles",
            "Streamlined workflow optimization",
            "Real-time updates (coming soon)",
            "OCR integration (coming soon)",
            "Email integration (coming soon)"
        ]
    }
