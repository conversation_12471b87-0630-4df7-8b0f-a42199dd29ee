import type { TaskContext, TaskPriority, TaskStatus } from '@/types';

// Context configurations for work-focused productivity
export const CONTEXTS: Record<TaskContext, {
  label: string;
  color: string;
  bgColor: string;
  description: string;
  icon: string;
}> = {
  work: {
    label: 'Work',
    color: 'text-work',
    bgColor: 'bg-work',
    description: 'Work tasks and projects',
    icon: '💼',
  },
};

// Priority configurations
export const PRIORITIES: Record<TaskPriority, {
  label: string;
  color: string;
  bgColor: string;
  order: number;
}> = {
  low: {
    label: 'Low',
    color: 'text-neutral-600',
    bgColor: 'bg-neutral-100',
    order: 1,
  },
  medium: {
    label: 'Medium',
    color: 'text-primary-600',
    bgColor: 'bg-primary-100',
    order: 2,
  },
  high: {
    label: 'High',
    color: 'text-accent-600',
    bgColor: 'bg-accent-100',
    order: 3,
  },
  urgent: {
    label: 'Urgent',
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    order: 4,
  },
};

// Status configurations
export const STATUSES: Record<TaskStatus, {
  label: string;
  color: string;
  bgColor: string;
  icon: string;
}> = {
  pending: {
    label: 'Pending',
    color: 'text-neutral-600',
    bgColor: 'bg-neutral-100',
    icon: '⏳',
  },
  in_progress: {
    label: 'In Progress',
    color: 'text-primary-600',
    bgColor: 'bg-primary-100',
    icon: '🔄',
  },
  completed: {
    label: 'Completed',
    color: 'text-secondary-600',
    bgColor: 'bg-secondary-100',
    icon: '✅',
  },
  cancelled: {
    label: 'Cancelled',
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    icon: '❌',
  },
};

// Work-focused design constants
export const DESIGN_CONSTANTS = {
  // Animation durations (reduced for better focus)
  ANIMATION_DURATION: {
    fast: 150,
    normal: 200,
    slow: 300,
  },
  
  // Spacing for visual clarity
  SPACING: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  
  // Border radius for soft, calming appearance
  BORDER_RADIUS: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
  },
  
  // Shadow levels for depth without overwhelm
  SHADOW: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
  },
};

// Default widget configurations
export const DEFAULT_WIDGETS = [
  {
    id: 'quick-add',
    type: 'quick-add' as const,
    title: 'Quick Add Task',
    position: { x: 0, y: 0 },
    size: { width: 6, height: 2 },
  },
  {
    id: 'task-stats',
    type: 'task-stats' as const,
    title: 'Task Overview',
    position: { x: 6, y: 0 },
    size: { width: 6, height: 2 },
  },
  {
    id: 'task-list',
    type: 'task-list' as const,
    title: 'Recent Tasks',
    position: { x: 0, y: 2 },
    size: { width: 12, height: 6 },
  },
];

// Local storage keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'mwe-dashboard-preferences',
  DASHBOARD_LAYOUT: 'mwe-dashboard-layout',
  ACTIVE_CONTEXT: 'mwe-dashboard-context',
} as const;
