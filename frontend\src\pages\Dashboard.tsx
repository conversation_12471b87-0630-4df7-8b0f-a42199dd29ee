import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, Filter } from 'lucide-react';
import { QuickAddTask } from '@/components/tasks/QuickAddTask';
import { TaskStatsWidget } from '@/components/widgets/TaskStatsWidget';
import { TaskCard } from '@/components/tasks/TaskCard';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { useTasks } from '@/hooks/useTasks';
import { sortTasks, cn } from '@/utils/helpers';
import { CONTEXTS } from '@/utils/constants';
import type { TaskContext } from '@/types';

export function Dashboard() {
  const [activeContext, setActiveContext] = useState<TaskContext | 'all'>('all');
  const { data: tasks = [], isLoading, error } = useTasks(
    activeContext === 'all' ? undefined : activeContext
  );

  const recentTasks = sortTasks(tasks).slice(0, 6);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-neutral-900 flex items-center">
            <Brain className="mr-3 h-8 w-8 text-primary-600" />
            Make Work Easy
          </h1>
          <p className="mt-1 text-neutral-600">
            Your focused workspace for getting things done
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5 text-accent-500" />
          <span className="text-sm text-neutral-600">Distraction-free zone</span>
        </div>
      </div>

      {/* Context Filter */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-neutral-500" />
              <span className="text-sm font-medium text-neutral-700">Focus Area:</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant={activeContext === 'all' ? 'primary' : 'ghost'}
                onClick={() => setActiveContext('all')}
              >
                All Tasks
              </Button>
              
              {Object.entries(CONTEXTS).map(([key, config]) => (
                <Button
                  key={key}
                  size="sm"
                  variant={activeContext === key ? 'primary' : 'ghost'}
                  onClick={() => setActiveContext(key as TaskContext)}
                  className="flex items-center space-x-1"
                >
                  <span>{config.icon}</span>
                  <span>{config.label}</span>
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Add Task */}
        <div className="space-y-6">
          <QuickAddTask />
          
          {/* Task Stats */}
          <TaskStatsWidget context={activeContext === 'all' ? undefined : activeContext} />
        </div>

        {/* Recent Tasks */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Tasks</CardTitle>
          </CardHeader>
          
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-20 bg-neutral-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600">Failed to load tasks</p>
                <p className="text-sm text-neutral-500 mt-1">
                  Please check your connection and try again
                </p>
              </div>
            ) : recentTasks.length === 0 ? (
              <div className="text-center py-8">
                <div className="mx-auto h-12 w-12 rounded-full bg-neutral-100 flex items-center justify-center mb-4">
                  <Brain className="h-6 w-6 text-neutral-400" />
                </div>
                <p className="text-neutral-500">No tasks yet</p>
                <p className="text-sm text-neutral-400 mt-1">
                  {activeContext === 'all' 
                    ? 'Create your first task to get started!'
                    : `No tasks in ${CONTEXTS[activeContext as TaskContext]?.label} context`
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto scrollbar-thin">
                {recentTasks.map((task) => (
                  <TaskCard key={task.id} task={task} compact />
                ))}
                
                {tasks.length > 6 && (
                  <div className="pt-3 border-t border-neutral-100">
                    <Button variant="ghost" size="sm" className="w-full">
                      View All Tasks ({tasks.length})
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Productivity Tips */}
      <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                <Brain className="h-4 w-4 text-primary-600" />
              </div>
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-primary-900 mb-1">Productivity Tip</h3>
              <p className="text-sm text-primary-700">
                Break large tasks into smaller, manageable chunks. Each small win builds momentum!
                Focus on one task at a time to maximize efficiency and reduce overwhelm.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
