import React from 'react';
import { Settings as <PERSON>ting<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

export function Settings() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-neutral-900">Settings</h1>
        <p className="mt-1 text-neutral-600">
          Customize your work productivity workspace
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* ADHD Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Brain className="mr-2 h-5 w-5" />
              Work Preferences
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">
                Duplicate Detection Threshold
              </label>
              <Input
                type="range"
                min="0.1"
                max="1.0"
                step="0.1"
                defaultValue="0.7"
                className="w-full"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Higher values = stricter duplicate detection
              </p>
            </div>

            <div className="text-center py-4">
              <div className="mx-auto h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center mb-4">
                <Brain className="h-6 w-6 text-primary-600" />
              </div>
              <p className="text-neutral-600 font-medium">Work-Focused Productivity</p>
              <p className="text-sm text-neutral-500 mt-1">
                This dashboard is designed specifically for work tasks and projects
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Appearance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Palette className="mr-2 h-5 w-5" />
              Appearance
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Theme
              </label>
              <div className="space-y-2">
                <label className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-50">
                  <input
                    type="radio"
                    name="theme"
                    value="light"
                    defaultChecked
                    className="text-primary-600 focus:ring-primary-500"
                  />
                  <span className="font-medium text-neutral-900">Light</span>
                  <span className="text-sm text-neutral-500">(Recommended for productivity)</span>
                </label>
                <label className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-50 opacity-50">
                  <input
                    type="radio"
                    name="theme"
                    value="dark"
                    disabled
                    className="text-primary-600 focus:ring-primary-500"
                  />
                  <span className="font-medium text-neutral-900">Dark</span>
                  <span className="text-sm text-neutral-500">(Coming soon)</span>
                </label>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Animation Speed
              </label>
              <select className="w-full rounded-lg border border-neutral-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                <option value="reduced">Reduced (Recommended)</option>
                <option value="normal">Normal</option>
                <option value="fast">Fast</option>
              </select>
              <p className="text-xs text-neutral-500 mt-1">
                Reduced animations help maintain focus
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="mr-2 h-5 w-5" />
              Notifications
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="text-center py-4">
              <Bell className="mx-auto h-8 w-8 text-neutral-400 mb-2" />
              <p className="text-neutral-500">Notification settings</p>
              <p className="text-sm text-neutral-400">Coming in a future update</p>
            </div>
          </CardContent>
        </Card>

        {/* Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Security & Privacy
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-50">
                <input
                  type="checkbox"
                  defaultChecked
                  className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                />
                <div className="flex-1">
                  <div className="font-medium text-neutral-900">Local Storage Only</div>
                  <div className="text-sm text-neutral-500">All data stays on your device</div>
                </div>
              </label>
              
              <label className="flex items-center space-x-3 p-2 rounded-lg hover:bg-neutral-50">
                <input
                  type="checkbox"
                  defaultChecked
                  className="rounded border-neutral-300 text-primary-600 focus:ring-primary-500"
                />
                <div className="flex-1">
                  <div className="font-medium text-neutral-900">Encrypted Sensitive Data</div>
                  <div className="text-sm text-neutral-500">Work notes and data are encrypted</div>
                </div>
              </label>
            </div>

            <div className="pt-4 border-t border-neutral-100">
              <Button variant="outline" size="sm" className="w-full">
                Export Data
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button>
          Save Settings
        </Button>
      </div>
    </div>
  );
}
