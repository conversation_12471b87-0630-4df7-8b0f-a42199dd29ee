# Make Work Easy Dashboard

A locally-hosted personal productivity dashboard designed to make work easy and streamline professional workflows.

## Features

- **Intelligent Task Management** with duplicate detection
- **OCR & Handwriting Recognition** for note capture
- **Smart Email Integration** for work tasks
- **Work-Focused UI** with customizable widgets
- **Real-time Updates** via WebSocket connections
- **Professional Security** for sensitive data

## Project Structure

```
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API endpoints
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── main.py         # FastAPI app
│   ├── alembic/            # Database migrations
│   ├── requirements.txt    # Python dependencies
│   └── Dockerfile          # Backend container
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utilities
│   ├── package.json        # Node dependencies
│   └── Dockerfile          # Frontend container
├── docker-compose.yml      # Development environment
├── .env.example           # Environment variables template
└── docs/                  # Documentation
```

## Quick Start

1. **Clone and setup:**
   ```bash
   git clone <repository>
   cd make-work-easy-dashboard
   cp .env.example .env
   ```

2. **Start development environment:**
   ```bash
   docker-compose up -d
   ```

3. **Access the application:**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:8000
   - API Docs: http://localhost:8000/docs

## Development

### Backend (FastAPI)
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend (React + Vite)
```bash
cd frontend
npm install
npm run dev
```

### Database Migrations
```bash
cd backend
alembic upgrade head
```

## Architecture

- **Backend**: FastAPI with PostgreSQL and Redis
- **Frontend**: React with Vite and TypeScript
- **Real-time**: WebSocket connections
- **Background Jobs**: Celery with Redis
- **OCR**: Tesseract + EasyOCR
- **Email**: Microsoft Graph API + IMAP/SMTP

## Security

- Multi-factor authentication
- Encrypted data storage
- VPN/reverse proxy support
- Audit logging for compliance

## License

Private project for personal productivity enhancement.
