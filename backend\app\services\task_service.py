from typing import List, Optional, Tu<PERSON>, Dict, Any
from sqlalchemy import select, func, and_
from sqlalchemy.ext.asyncio import AsyncSession
from ..models.task import Task, TaskStatus, TaskPriority, TaskContext
from ..core.config import settings
from .duplicate_detection import DuplicateDetectionService, DuplicateMatch
from datetime import datetime
import difflib
import logging

logger = logging.getLogger(__name__)


class TaskService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.duplicate_detector = DuplicateDetectionService()
    
    async def check_duplicates(self, title: str, description: str = None,
                              context: TaskContext = None, user_id: str = None,
                              threshold: float = None) -> List[DuplicateMatch]:
        """
        Check for potential duplicate tasks using advanced fuzzy matching algorithms
        """
        try:
            # Get all non-cancelled tasks for comparison
            all_tasks_stmt = select(Task).where(Task.status != TaskStatus.CANCELLED)
            all_tasks_result = await self.db.execute(all_tasks_stmt)
            all_tasks = all_tasks_result.scalars().all()

            # Use the advanced duplicate detection service
            matches = self.duplicate_detector.find_duplicates(
                new_title=title,
                new_description=description or "",
                existing_tasks=all_tasks,
                new_context=context,
                new_created_at=datetime.now()
            )

            return matches

        except Exception as e:
            logger.error(f"Error checking duplicates: {e}")
            return []
    
    async def create_task(self, 
                         title: str, 
                         description: str = None, 
                         context: TaskContext = TaskContext.WORK,
                         priority: TaskPriority = TaskPriority.MEDIUM,
                         user_id: str = None,
                         check_duplicates: bool = True,
                         source_type: str = "manual") -> Dict[str, Any]:
        """
        Create a new task with optional duplicate checking
        """
        try:
            if check_duplicates:
                duplicates = await self.check_duplicates(title, description, context, user_id)
                if duplicates:
                    return {
                        "status": "duplicate_warning",
                        "duplicates": [match.to_dict() for match in duplicates],
                        "message": "Similar tasks found. Create anyway?",
                        "total_matches": len(duplicates),
                        "highest_similarity": max(match.similarity_score for match in duplicates) if duplicates else 0
                    }
            
            new_task = Task(
                title=title,
                description=description,
                context=context,
                priority=priority,
                source_type=source_type
            )
            
            self.db.add(new_task)
            await self.db.commit()
            await self.db.refresh(new_task)
            
            logger.info(f"Created new task: {new_task.id}")
            
            return {
                "status": "created",
                "task": {
                    "id": str(new_task.id),
                    "title": new_task.title,
                    "description": new_task.description,
                    "context": new_task.context.value if new_task.context else None,
                    "priority": new_task.priority.value if new_task.priority else None,
                    "status": new_task.status.value if new_task.status else None,
                    "created_at": new_task.created_at.isoformat() if new_task.created_at else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error creating task: {e}")
            await self.db.rollback()
            raise
    
    async def get_tasks(self, 
                       user_id: str = None,
                       context: TaskContext = None,
                       status: TaskStatus = None,
                       limit: int = 100,
                       offset: int = 0) -> List[Task]:
        """
        Get tasks with optional filtering
        """
        try:
            stmt = select(Task)
            
            # Add filters
            conditions = []
            if context:
                conditions.append(Task.context == context)
            if status:
                conditions.append(Task.status == status)
                
            if conditions:
                stmt = stmt.where(and_(*conditions))
            
            stmt = stmt.order_by(Task.created_at.desc()).limit(limit).offset(offset)
            
            result = await self.db.execute(stmt)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Error getting tasks: {e}")
            return []
    
    async def update_task_status(self, task_id: str, status: TaskStatus) -> Optional[Task]:
        """
        Update task status
        """
        try:
            stmt = select(Task).where(Task.id == task_id)
            result = await self.db.execute(stmt)
            task = result.scalar_one_or_none()
            
            if not task:
                return None
                
            task.status = status
            if status == TaskStatus.COMPLETED:
                task.completed = True
            else:
                task.completed = False
                
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info(f"Updated task {task_id} status to {status}")
            return task
            
        except Exception as e:
            logger.error(f"Error updating task status: {e}")
            await self.db.rollback()
            return None
