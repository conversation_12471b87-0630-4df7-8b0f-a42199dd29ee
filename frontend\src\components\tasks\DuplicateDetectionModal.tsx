import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, X, <PERSON>, <PERSON>, Clock, Target, Zap, Merge } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CONTEXTS, PRIORITIES } from '@/utils/constants';
import type { DuplicateMatch, TaskContext, TaskPriority } from '@/types';

interface DuplicateDetectionModalProps {
  isOpen: boolean;
  duplicates: DuplicateMatch[];
  newTaskTitle: string;
  newTaskDescription?: string;
  onClose: () => void;
  onCreateAnyway: () => void;
  onMergeWithTask?: (taskId: string, newTitle: string, newDescription?: string) => void;
  onViewTask?: (taskId: string) => void;
  isLoading?: boolean;
}

const getMatchTypeIcon = (matchType: string) => {
  switch (matchType) {
    case 'exact':
      return <Target className="h-4 w-4 text-red-500" />;
    case 'fuzzy_token_set':
      return <Zap className="h-4 w-4 text-orange-500" />;
    case 'fuzzy_token_sort':
      return <Zap className="h-4 w-4 text-yellow-500" />;
    case 'fuzzy_partial':
      return <Zap className="h-4 w-4 text-blue-500" />;
    case 'semantic':
      return <Target className="h-4 w-4 text-purple-500" />;
    case 'temporal':
      return <Clock className="h-4 w-4 text-green-500" />;
    default:
      return <Zap className="h-4 w-4 text-gray-500" />;
  }
};

const getMatchTypeLabel = (matchType: string) => {
  switch (matchType) {
    case 'exact':
      return 'Exact Match';
    case 'fuzzy_token_set':
      return 'Similar Words';
    case 'fuzzy_token_sort':
      return 'Reordered Words';
    case 'fuzzy_partial':
      return 'Partial Match';
    case 'semantic':
      return 'Similar Meaning';
    case 'temporal':
      return 'Time-based';
    default:
      return 'Fuzzy Match';
  }
};

const getConfidenceColor = (confidence: number) => {
  if (confidence >= 0.9) return 'text-red-600 bg-red-50 border-red-200';
  if (confidence >= 0.8) return 'text-orange-600 bg-orange-50 border-orange-200';
  if (confidence >= 0.7) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  return 'text-blue-600 bg-blue-50 border-blue-200';
};

export function DuplicateDetectionModal({
  isOpen,
  duplicates,
  newTaskTitle,
  newTaskDescription,
  onClose,
  onCreateAnyway,
  onMergeWithTask,
  onViewTask,
  isLoading = false
}: DuplicateDetectionModalProps) {
  if (!isOpen) return null;

  const [selectedForMerge, setSelectedForMerge] = useState<string | null>(null);
  const highestSimilarity = Math.max(...duplicates.map(d => d.similarity_score));
  const averageConfidence = duplicates.reduce((sum, d) => sum + d.confidence, 0) / duplicates.length;

  const handleMerge = (taskId: string) => {
    if (onMergeWithTask) {
      onMergeWithTask(taskId, newTaskTitle, newTaskDescription);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden bg-white rounded-lg shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-accent-50">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-6 w-6 text-accent-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Potential Duplicates Found
              </h2>
              <p className="text-sm text-gray-600">
                Found {duplicates.length} similar task{duplicates.length > 1 ? 's' : ''} for "{newTaskTitle}"
              </p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Summary Stats */}
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(highestSimilarity * 100)}%
              </div>
              <div className="text-sm text-gray-600">Highest Similarity</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(averageConfidence * 100)}%
              </div>
              <div className="text-sm text-gray-600">Avg Confidence</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {duplicates.length}
              </div>
              <div className="text-sm text-gray-600">Total Matches</div>
            </div>
          </div>
        </div>

        {/* Duplicates List */}
        <div className="p-6 max-h-96 overflow-y-auto">
          <div className="space-y-4">
            {duplicates.map((duplicate, index) => (
              <Card key={duplicate.id} className="border-l-4 border-l-accent-400">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Task Title and Similarity */}
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900 text-lg">
                          {duplicate.title}
                        </h3>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getConfidenceColor(duplicate.confidence)}`}>
                            {Math.round(duplicate.similarity_score * 100)}% similar
                          </span>
                        </div>
                      </div>

                      {/* Description */}
                      {duplicate.description && (
                        <p className="text-sm text-gray-600 mb-3">
                          {duplicate.description}
                        </p>
                      )}

                      {/* Match Details */}
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="flex items-center space-x-1">
                          {getMatchTypeIcon(duplicate.match_type)}
                          <span className="text-xs text-gray-500">
                            {getMatchTypeLabel(duplicate.match_type)}
                          </span>
                        </div>
                        
                        {duplicate.context && (
                          <span className="text-xs text-gray-500">
                            {CONTEXTS[duplicate.context as TaskContext]?.icon} {CONTEXTS[duplicate.context as TaskContext]?.label}
                          </span>
                        )}
                        
                        {duplicate.priority && (
                          <span className="text-xs text-gray-500">
                            {PRIORITIES[duplicate.priority as TaskPriority]?.label} Priority
                          </span>
                        )}
                        
                        {duplicate.created_at && (
                          <span className="text-xs text-gray-500">
                            Created {new Date(duplicate.created_at).toLocaleDateString()}
                          </span>
                        )}
                      </div>

                      {/* Reasons */}
                      <div className="flex flex-wrap gap-1">
                        {duplicate.reasons.map((reason, reasonIndex) => (
                          <span
                            key={reasonIndex}
                            className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full"
                          >
                            {reason}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="ml-4 flex flex-col space-y-2">
                      {onViewTask && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onViewTask(duplicate.id)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      )}
                      {onMergeWithTask && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMerge(duplicate.id)}
                          className="text-blue-600 border-blue-200 hover:bg-blue-50"
                        >
                          <Merge className="h-4 w-4 mr-1" />
                          Merge
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Review the similar tasks above or proceed to create your new task.
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={onClose}>
              Review & Edit
            </Button>
            <Button 
              onClick={onCreateAnyway}
              loading={isLoading}
              className="bg-accent-600 hover:bg-accent-700"
            >
              <Check className="h-4 w-4 mr-2" />
              Create Anyway
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
