import React from 'react';
import { FileText, Plus, Camera, Mic } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export function Notes() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-neutral-900">Notes</h1>
        <p className="mt-1 text-neutral-600">
          Capture work-related thoughts, ideas, and important information
        </p>
      </div>

      {/* Coming Soon Notice */}
      <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
        <CardContent className="p-8 text-center">
          <div className="mx-auto h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center mb-4">
            <FileText className="h-8 w-8 text-primary-600" />
          </div>
          
          <h2 className="text-xl font-semibold text-primary-900 mb-2">
            Notes Feature Coming Soon!
          </h2>
          
          <p className="text-primary-700 mb-6 max-w-md mx-auto">
            We're building an amazing work-focused note-taking experience with OCR support,
            handwriting recognition, and seamless task integration.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <div className="bg-white rounded-lg p-4 border border-primary-200">
              <Camera className="h-6 w-6 text-primary-600 mx-auto mb-2" />
              <h3 className="font-medium text-primary-900 mb-1">OCR Scanning</h3>
              <p className="text-sm text-primary-700">
                Scan handwritten notes and convert them to digital text
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-primary-200">
              <Mic className="h-6 w-6 text-primary-600 mx-auto mb-2" />
              <h3 className="font-medium text-primary-900 mb-1">Voice Notes</h3>
              <p className="text-sm text-primary-700">
                Record voice memos and convert them to text automatically
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-4 border border-primary-200">
              <Plus className="h-6 w-6 text-primary-600 mx-auto mb-2" />
              <h3 className="font-medium text-primary-900 mb-1">Task Integration</h3>
              <p className="text-sm text-primary-700">
                Convert notes into actionable tasks with one click
              </p>
            </div>
          </div>

          <div className="mt-6">
            <Button variant="outline" disabled>
              Coming in Phase 2
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Placeholder for future notes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="opacity-50">
            <CardContent className="p-4">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-neutral-200 rounded w-3/4"></div>
                <div className="h-3 bg-neutral-200 rounded"></div>
                <div className="h-3 bg-neutral-200 rounded w-5/6"></div>
                <div className="h-3 bg-neutral-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
