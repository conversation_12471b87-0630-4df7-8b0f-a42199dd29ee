"""
Advanced duplicate detection service for tasks using multiple fuzzy matching algorithms.
"""

from typing import List, Tuple, Dict, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
import re
import logging
from datetime import datetime, timedelta

from thefuzz import fuzz, process
from thefuzz.utils import full_process
import difflib

from ..models.task import Task, TaskStatus, TaskContext
from ..core.config import settings

logger = logging.getLogger(__name__)


class MatchType(Enum):
    """Types of duplicate matches"""
    EXACT = "exact"
    FUZZY_RATIO = "fuzzy_ratio"
    FUZZY_PARTIAL = "fuzzy_partial"
    FUZZY_TOKEN_SORT = "fuzzy_token_sort"
    FUZZY_TOKEN_SET = "fuzzy_token_set"
    SEMANTIC = "semantic"
    TEMPORAL = "temporal"


@dataclass
class DuplicateMatch:
    """Represents a potential duplicate match"""
    task: Task
    similarity_score: float
    match_type: MatchType
    confidence: float
    reasons: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "id": str(self.task.id),
            "title": self.task.title,
            "description": self.task.description,
            "context": self.task.context.value if self.task.context else None,
            "status": self.task.status.value if self.task.status else None,
            "priority": self.task.priority.value if self.task.priority else None,
            "created_at": self.task.created_at.isoformat() if self.task.created_at else None,
            "similarity_score": round(self.similarity_score, 3),
            "match_type": self.match_type.value,
            "confidence": round(self.confidence, 3),
            "reasons": self.reasons
        }


class DuplicateDetectionService:
    """Advanced duplicate detection service using multiple algorithms"""
    
    def __init__(self):
        self.threshold = settings.duplicate_detection_threshold
        self.max_suggestions = settings.max_duplicate_suggestions
        self.context_boost = settings.duplicate_detection_context_boost
        
        # Common words to ignore in fuzzy matching
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can'
        }
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess text for better matching"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common punctuation that doesn't affect meaning
        text = re.sub(r'[^\w\s-]', '', text)
        
        return text
    
    def extract_keywords(self, text: str) -> Set[str]:
        """Extract meaningful keywords from text"""
        processed = self.preprocess_text(text)
        words = processed.split()
        
        # Filter out stop words and short words
        keywords = {
            word for word in words 
            if len(word) > 2 and word not in self.stop_words
        }
        
        return keywords
    
    def calculate_fuzzy_scores(self, text1: str, text2: str) -> Dict[str, float]:
        """Calculate various fuzzy matching scores"""
        processed1 = self.preprocess_text(text1)
        processed2 = self.preprocess_text(text2)
        
        return {
            'ratio': fuzz.ratio(processed1, processed2) / 100.0,
            'partial_ratio': fuzz.partial_ratio(processed1, processed2) / 100.0,
            'token_sort_ratio': fuzz.token_sort_ratio(processed1, processed2) / 100.0,
            'token_set_ratio': fuzz.token_set_ratio(processed1, processed2) / 100.0,
            'difflib_ratio': difflib.SequenceMatcher(None, processed1, processed2).ratio()
        }
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity based on keywords"""
        keywords1 = self.extract_keywords(text1)
        keywords2 = self.extract_keywords(text2)
        
        if not keywords1 or not keywords2:
            return 0.0
        
        # Jaccard similarity
        intersection = keywords1.intersection(keywords2)
        union = keywords1.union(keywords2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def calculate_temporal_similarity(self, task1_created: datetime, task2_created: datetime) -> float:
        """Calculate temporal similarity (tasks created close in time might be duplicates)"""
        if not task1_created or not task2_created:
            return 0.0
        
        time_diff = abs((task1_created - task2_created).total_seconds())
        
        # Consider tasks created within 1 hour as potentially related
        max_time_diff = 3600  # 1 hour in seconds
        
        if time_diff > max_time_diff:
            return 0.0
        
        # Linear decay: 1.0 at 0 seconds, 0.0 at max_time_diff
        return 1.0 - (time_diff / max_time_diff)
    
    def analyze_duplicate(self, new_title: str, new_description: str, existing_task: Task, 
                         new_created_at: Optional[datetime] = None) -> Optional[DuplicateMatch]:
        """Analyze if an existing task is a duplicate of the new task"""
        
        # Calculate fuzzy scores for title
        title_scores = self.calculate_fuzzy_scores(new_title, existing_task.title)
        
        # Calculate fuzzy scores for description if both exist
        desc_scores = {}
        if new_description and existing_task.description:
            desc_scores = self.calculate_fuzzy_scores(new_description, existing_task.description)
        
        # Calculate semantic similarity
        semantic_score = self.calculate_semantic_similarity(new_title, existing_task.title)
        
        # Calculate temporal similarity
        temporal_score = 0.0
        if new_created_at:
            temporal_score = self.calculate_temporal_similarity(new_created_at, existing_task.created_at)
        
        # Determine the best match type and score
        best_score = 0.0
        match_type = MatchType.FUZZY_RATIO
        reasons = []
        
        # Check exact match first
        if title_scores['ratio'] >= 0.98:
            best_score = title_scores['ratio']
            match_type = MatchType.EXACT
            reasons.append("Nearly identical titles")
        
        # Check various fuzzy matching algorithms
        elif title_scores['token_set_ratio'] > best_score:
            best_score = title_scores['token_set_ratio']
            match_type = MatchType.FUZZY_TOKEN_SET
            reasons.append("Similar word sets in titles")
        
        elif title_scores['token_sort_ratio'] > best_score:
            best_score = title_scores['token_sort_ratio']
            match_type = MatchType.FUZZY_TOKEN_SORT
            reasons.append("Similar words in different order")
        
        elif title_scores['partial_ratio'] > best_score:
            best_score = title_scores['partial_ratio']
            match_type = MatchType.FUZZY_PARTIAL
            reasons.append("Partial title match")
        
        elif title_scores['ratio'] > best_score:
            best_score = title_scores['ratio']
            match_type = MatchType.FUZZY_RATIO
            reasons.append("Similar titles")
        
        # Boost score if descriptions are similar
        if desc_scores and max(desc_scores.values()) > 0.7:
            best_score = min(1.0, best_score + 0.1)
            reasons.append("Similar descriptions")
        
        # Boost score for semantic similarity
        if semantic_score > 0.5:
            best_score = min(1.0, best_score + (semantic_score * 0.2))
            reasons.append("Similar keywords")
        
        # Boost score for temporal proximity
        if temporal_score > 0.3:
            best_score = min(1.0, best_score + (temporal_score * 0.1))
            reasons.append("Created around the same time")
        
        # Calculate confidence based on multiple factors
        confidence = best_score
        if len(reasons) > 1:
            confidence = min(1.0, confidence + 0.1)  # Boost confidence for multiple reasons
        
        # Only return matches above threshold
        if best_score >= self.threshold:
            return DuplicateMatch(
                task=existing_task,
                similarity_score=best_score,
                match_type=match_type,
                confidence=confidence,
                reasons=reasons
            )
        
        return None
    
    def find_duplicates(self, new_title: str, new_description: str, existing_tasks: List[Task],
                       new_context: Optional[TaskContext] = None,
                       new_created_at: Optional[datetime] = None) -> List[DuplicateMatch]:
        """Find potential duplicates among existing tasks"""
        
        matches = []
        
        for task in existing_tasks:
            # Skip cancelled tasks
            if task.status == TaskStatus.CANCELLED:
                continue
            
            # Prioritize tasks in the same context
            context_boost = 0.0
            if new_context and task.context == new_context:
                context_boost = self.context_boost
            
            match = self.analyze_duplicate(new_title, new_description, task, new_created_at)
            
            if match:
                # Apply context boost
                match.similarity_score = min(1.0, match.similarity_score + context_boost)
                match.confidence = min(1.0, match.confidence + context_boost)
                
                if context_boost > 0:
                    match.reasons.append("Same work context")
                
                matches.append(match)
        
        # Sort by similarity score (descending) and limit results
        matches.sort(key=lambda x: x.similarity_score, reverse=True)
        return matches[:self.max_suggestions]
