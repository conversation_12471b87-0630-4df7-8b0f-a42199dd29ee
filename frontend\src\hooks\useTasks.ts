import { useQuery, useMutation, useQueryClient } from 'react-query';
import { taskApi } from '@/services/api';
import type { 
  Task, 
  TaskCreateRequest, 
  TaskCreateResponse, 
  TaskContext, 
  TaskStatus 
} from '@/types';
import toast from 'react-hot-toast';

// Hook for fetching tasks
export const useTasks = (context?: TaskContext, status?: TaskStatus) => {
  return useQuery(
    ['tasks', context, status],
    () => taskApi.getTasks({ context, status }),
    {
      staleTime: 30000, // 30 seconds
      refetchOnWindowFocus: false,
      onError: (error: any) => {
        toast.error('Failed to fetch tasks');
        console.error('Error fetching tasks:', error);
      },
    }
  );
};

// Hook for creating tasks
export const useCreateTask = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (data: TaskCreateRequest) => taskApi.createTask(data),
    {
      onSuccess: (response: TaskCreateResponse) => {
        if (response.status === 'created') {
          toast.success('Task created successfully!');
          // Invalidate and refetch tasks
          queryClient.invalidateQueries(['tasks']);
          queryClient.invalidateQueries(['task-stats']);
        }
      },
      onError: (error: any) => {
        toast.error('Failed to create task');
        console.error('Error creating task:', error);
      },
    }
  );
};

// Hook for updating task status
export const useUpdateTaskStatus = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ taskId, status }: { taskId: string; status: TaskStatus }) =>
      taskApi.updateTaskStatus(taskId, status),
    {
      onSuccess: (updatedTask: Task) => {
        toast.success(`Task marked as ${updatedTask.status.replace('_', ' ')}`);
        // Invalidate and refetch tasks
        queryClient.invalidateQueries(['tasks']);
        queryClient.invalidateQueries(['task-stats']);
      },
      onError: (error: any) => {
        toast.error('Failed to update task');
        console.error('Error updating task:', error);
      },
    }
  );
};

// Hook for checking duplicates
export const useCheckDuplicates = () => {
  return useMutation(
    ({ title, threshold }: { title: string; threshold?: number }) =>
      taskApi.checkDuplicates(title, threshold),
    {
      onError: (error: any) => {
        console.error('Error checking duplicates:', error);
      },
    }
  );
};

// Hook for task statistics
export const useTaskStats = (context?: TaskContext) => {
  return useQuery(
    ['task-stats', context],
    () => taskApi.getTaskStats(context),
    {
      staleTime: 60000, // 1 minute
      refetchOnWindowFocus: false,
      onError: (error: any) => {
        console.error('Error fetching task stats:', error);
      },
    }
  );
};
