import axios from 'axios';
import type {
  Task,
  TaskCreateRequest,
  TaskCreateResponse,
  TaskStats,
  DuplicateTask,
  DuplicateMatch,
  TaskContext,
  TaskStatus,
  AppSettings,
  DuplicateDetectionSettings
} from '@/types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use((config) => {
  console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const taskApi = {
  // Create a new task
  async createTask(data: TaskCreateRequest): Promise<TaskCreateResponse> {
    const response = await api.post<TaskCreateResponse>('/api/tasks/', data);
    return response.data;
  },

  // Get all tasks with optional filtering
  async getTasks(params?: {
    context?: TaskContext;
    status?: TaskStatus;
    limit?: number;
    offset?: number;
  }): Promise<Task[]> {
    const response = await api.get<Task[]>('/api/tasks/', { params });
    return response.data;
  },

  // Check for duplicate tasks
  async checkDuplicates(title: string, description?: string, context?: TaskContext, threshold = 0.7): Promise<DuplicateMatch[]> {
    const response = await api.get<{ potential_duplicates: DuplicateMatch[] }>(
      '/api/tasks/check-duplicates',
      {
        params: {
          title,
          description,
          context,
          threshold
        }
      }
    );
    return response.data.potential_duplicates;
  },

  // Update task
  async updateTask(taskId: string, data: Partial<TaskCreateRequest>): Promise<Task> {
    const response = await api.patch<Task>(`/api/tasks/${taskId}`, data);
    return response.data;
  },

  // Update task status
  async updateTaskStatus(taskId: string, status: TaskStatus): Promise<Task> {
    const response = await api.patch<Task>(`/api/tasks/${taskId}/status`, { status });
    return response.data;
  },

  // Get task statistics
  async getTaskStats(context?: TaskContext): Promise<TaskStats> {
    const response = await api.get<TaskStats>('/api/tasks/stats', {
      params: context ? { context } : undefined
    });
    return response.data;
  },
};

export const healthApi = {
  // Check API health
  async checkHealth(): Promise<{ status: string; service: string; version: string }> {
    const response = await api.get('/health');
    return response.data;
  },
};

// Settings API
export const settingsApi = {
  // Get all settings
  async getSettings(): Promise<AppSettings> {
    const response = await api.get<AppSettings>('/api/settings/');
    return response.data;
  },

  // Update settings
  async updateSettings(settings: Partial<AppSettings>): Promise<AppSettings> {
    const response = await api.patch<AppSettings>('/api/settings/', settings);
    return response.data;
  },

  // Get duplicate detection settings
  async getDuplicateDetectionSettings(): Promise<DuplicateDetectionSettings> {
    const response = await api.get<DuplicateDetectionSettings>('/api/settings/duplicate-detection');
    return response.data;
  },

  // Update duplicate detection settings
  async updateDuplicateDetectionSettings(settings: DuplicateDetectionSettings): Promise<DuplicateDetectionSettings> {
    const response = await api.patch<DuplicateDetectionSettings>('/api/settings/duplicate-detection', settings);
    return response.data;
  },

  // Reset settings to defaults
  async resetSettings(): Promise<{ message: string }> {
    const response = await api.post<{ message: string }>('/api/settings/reset');
    return response.data;
  },
};

export default api;
