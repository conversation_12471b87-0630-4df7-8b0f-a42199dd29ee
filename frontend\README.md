# Make Work Easy Dashboard - Frontend

A React-based frontend for the Make Work Easy Dashboard, designed with work-focused productivity principles.

## Features

- **Work-Focused Design**: Minimal distractions, clear focus areas, professional interface
- **Task Management**: Create, organize, and track work tasks and projects
- **Streamlined Workflow**: Optimized for work productivity and efficiency
- **Duplicate Detection**: Smart duplicate task detection to reduce redundancy
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## Tech Stack

- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **React Router** for navigation
- **React Query** for data fetching and caching
- **React Hot Toast** for notifications
- **Lucide React** for icons

## Getting Started

### Prerequisites

- Node.js 16+ 
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Card, Input)
│   ├── tasks/          # Task-related components
│   └── widgets/        # Dashboard widgets
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── services/           # API services
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and constants
└── index.css           # Global styles and Tailwind imports
```

## Design Principles

### Work-Focused Features

1. **Reduced Cognitive Load**
   - Minimal animations (can be disabled)
   - Clear visual hierarchy
   - Professional color palette
   - Consistent spacing

2. **Productivity Support**
   - Work-focused organization
   - Clear task priorities
   - Distraction-free interface
   - Quick actions

3. **Accessibility**
   - High contrast ratios
   - Keyboard navigation
   - Screen reader support
   - Focus indicators

## Environment Variables

- `VITE_API_URL` - Backend API URL (default: http://localhost:8000)
- `VITE_DEV_MODE` - Enable development features
- `VITE_ENABLE_NOTES` - Enable notes feature (coming soon)
- `VITE_ENABLE_OCR` - Enable OCR scanning (coming soon)
- `VITE_ENABLE_VOICE_NOTES` - Enable voice notes (coming soon)

## Contributing

1. Follow the existing code style
2. Use TypeScript for type safety
3. Keep components small and focused
4. Maintain work-focused design principles
5. Test on multiple screen sizes

## License

This project is part of the Make Work Easy Dashboard suite.
